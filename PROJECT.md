# PROJECT.md

# MinerU 專案總覽

## 專案名稱
**MinerU**

## 專案簡介
MinerU 是一個高效能的多模態文件解析工具，專注於 PDF 及複雜文件的結構化資訊抽取。其 2.0 版本進行了全面重構，提升了架構、效能與用戶體驗，並整合了最新的小參數高效能多模態模型，支援多語言辨識、手寫辨識、版面分析、表格解析、公式辨識、閱讀順序排序等多項核心任務。

## 主要功能
- **多模態文件解析**：支援文字、表格、公式、手寫等多種內容的結構化抽取。
- **高效能模型**：內建小於 1B 參數的高效能 VLM 模型，解析精度超越傳統 72B 級模型。
- **自動模型管理**：自動下載與更新模型，支援離線部署。
- **統一中介格式**：採用 `middle_json` 標準格式，便於二次開發與生態整合。
- **批次處理與高吞吐**：支援多檔案批次處理，單卡可達萬 tokens/s。
- **易於安裝與部署**：可透過 pip、Docker、Colab、HuggingFace、ModelScope 等多種方式體驗。

## 主要依賴 (以 web_api 為例)
- `magic-pdf[full]`
- `fastapi`
- `uvicorn`
- `python-multipart`

## 執行入口與用法

### 1. Python 套件
- 安裝：`pip install mineru`
- 命令列工具：`mineru`（原 `magic-pdf` 已更名）
- 主要 API 與 CLI 入口位於 `mineru/cli/client.py`、`mineru/backend/pipeline/pipeline_analyze.py` 等。

### 2. Web API 服務
- 位置：`projects/web_api/`
- 主要入口：`app.py`
- 啟動方式（Docker）：
  ```sh
  docker build -t mineru-api .
  docker run --rm -it --gpus=all -p 8000:8000 mineru-api
  ```
- API 文件自動生成於：
  - http://localhost:8000/docs

### 3. Demo 與範例
- Colab 線上體驗：[Colab Demo](https://colab.research.google.com/gist/myhloli/3b3a00a4a0a61577b6c30f989092d20d/mineru_demo.ipynb)
- HuggingFace Demo：[HuggingFace Space](https://huggingface.co/spaces/opendatalab/MinerU)
- ModelScope Demo：[ModelScope Studio](https://www.modelscope.cn/studios/OpenDataLab/MinerU)
- 本地批次處理範例：`demo/batch_demo.py`
- 更多範例與說明請參見主目錄下 `README.md` 及 `projects/web_api/README.md`

## 版本與更新紀錄
- 2025/06/20 v2.0.6：修復 VLM 模式下區塊內容異常導致解析中斷等問題
- 2025/06/17 v2.0.5：修復 sglang-client 模式下模型下載與依賴問題
- 2025/06/15 v2.0.3：修復模型下載與命令列開關問題，支援 sglang 0.4.7
- 2025/06/13 v2.0.0：全面重構，移除 pymupdf 依賴，統一命令與格式，提升易用性與效能

> 更詳細的更新日誌請參見 `README.md` 內的 Changelog 與歷史紀錄。

## 相關資源
- 官方網站：[MinerU.net](https://mineru.net/OpenSourceTools/Extractor)
- GitHub Repo：[https://github.com/opendatalab/MinerU](https://github.com/opendatalab/MinerU)
- Discord 社群、WeChat、Trendshift、論文連結等詳見 `README.md` 首段

---

# 繁體中文說明

## 專案分析摘要

1. **專案名稱與定位**：本專案名稱為 MinerU，是一套專注於 PDF 及複雜文件結構化解析的多模態 AI 工具，支援多語言、手寫、表格、公式等多種內容的高效抽取。
2. **主要功能**：包含高效能小參數 VLM 模型、批次處理、離線部署、自動模型管理、統一格式輸出等，適合大規模文件處理與二次開發。
3. **依賴套件**：以 web_api 為例，主要依賴 magic-pdf[full]、fastapi、uvicorn、python-multipart 等，主程式本身則以 mineru 為核心。
4. **執行入口**：命令列工具為 `mineru`，Web API 主要入口為 `projects/web_api/app.py`，可用 Docker 快速部署。
5. **用法與範例**：官方提供 Colab、HuggingFace、ModelScope 線上體驗，亦可本地批次處理或透過 API 服務。
6. **版本與更新**：2.0 版為重大重構，移除部分依賴、提升效能與易用性，詳細更新日誌見 README.md。
7. **文件與說明**：主說明文件為 `README.md`，API 與部署說明見 `projects/web_api/README.md`，目前未發現獨立的 HISTORY.md 或 PROJECT.md，故本文件即為最新專案總覽。

如需更詳細的技術細節、API 參數或進階用法，建議參閱主目錄下的 `README.md` 及相關子目錄說明文件。